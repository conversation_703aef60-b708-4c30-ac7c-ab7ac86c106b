import {createHash} from "crypto";
import fetch from "node-fetch";

// Helper function for delays
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

function generateSHA256Hash(input) {
  return createHash('sha256').update(input).digest('hex');
}

async function createAntiCheatHash(walletAddress, stats) {
  const generateValidationString = (wallet, wpm, accuracy, time, correct, incorrect) => {
    const combined = correct + incorrect;
    let baseValue = 0 + 23 * wpm + 89 * accuracy + 41 * time + 67 * correct + 13 * incorrect + 97 * combined;
    
    let charSum = 0;
    for (let i = 0; i < wallet.length; i++) {
      charSum += wallet.charCodeAt(i) * (i + 1);
    }
    
    baseValue += 31 * charSum;
    const finalHash = Math.floor(0x178ba57548d * baseValue % Number.MAX_SAFE_INTEGER);
    
    return `${wallet.toLowerCase()}_${wpm}_${accuracy}_${time}_${correct}_${incorrect}_${finalHash}`;
  };

  const validationString = generateValidationString(
    walletAddress, 
    stats.wpm, 
    stats.accuracy, 
    stats.time, 
    stats.correctChars, 
    stats.incorrectChars
  );
  
  const fullHash = generateSHA256Hash(validationString);
  return fullHash.substring(0, 32);
}

function generateHumanLikeStats() {
  const timeOptions = [15, 30, 60, 120];
  const selectedTime = timeOptions[Math.floor(Math.random() * timeOptions.length)];
  
  const baseWPM = Math.floor(Math.random() * 40) + 35;
  const wpmVariation = Math.floor(Math.random() * 10) - 5;
  const finalWPM = Math.max(25, baseWPM + wpmVariation);
  
  const accuracyBase = Math.floor(Math.random() * 15) + 85;
  const accuracy = Math.min(100, accuracyBase + Math.floor(Math.random() * 5));
  
  const avgCharsPerWord = 5;
  const totalCharsTyped = Math.floor((finalWPM * avgCharsPerWord * selectedTime) / 60);
  const correctChars = Math.floor((totalCharsTyped * accuracy) / 100);
  const incorrectChars = totalCharsTyped - correctChars;
  
  return {
    wpm: finalWPM,
    accuracy: accuracy,
    time: selectedTime,
    correctChars: correctChars,
    incorrectChars: Math.max(0, incorrectChars),
    progressData: []
  };
}

async function submitTypingResult({ maxRetries = 3, baseDelayMs = 1500 } = {}) {
  const walletAddress = "******************************************"; //address mu
  const gameStats = generateHumanLikeStats();

  console.log(`Generated stats: ${gameStats.wpm} WPM, ${gameStats.accuracy}% accuracy, ${gameStats.time}s`);

  const antiCheatHash = await createAntiCheatHash(walletAddress, gameStats);
  const submissionPayload = {
    walletAddress,
    gameStats,
    antiCheatHash,
    timestamp: Date.now()
  };

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Attempt ${attempt}/${maxRetries}...`);

      const response = await fetch("https://spritetype.irys.xyz/api/submit-result", {
        method: "POST",
        headers: {
          "accept": "*/*",
          "accept-language": "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7",
          "content-type": "application/json",
          "origin": "https://spritetype.irys.xyz",
          "referer": "https://spritetype.irys.xyz/",
          "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        },
        body: JSON.stringify(submissionPayload)
      });

      // Check if response is successful
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.text();
      console.log("Submission result:", result);

      if (result.includes('"success":true')) {
        console.log("✅ Successfully submitted to leaderboard!");
        return result;
      } else {
        console.log("❌ Submission failed - server returned failure");
        throw new Error("Server returned failure response");
      }

    } catch (error) {
      console.error(`Attempt ${attempt} failed:`, error.message);

      // Don't retry for certain errors (4xx except 429)
      if (error.message.includes('HTTP 4') && !error.message.includes('HTTP 429')) {
        console.log("❌ Client error - not retrying");
        throw error;
      }

      // If this was the last attempt, throw the error
      if (attempt === maxRetries) {
        console.log("❌ All retry attempts exhausted");
        throw error;
      }

      // Calculate exponential backoff delay
      const backoffDelay = baseDelayMs * Math.pow(2, attempt - 1);
      console.log(`⏳ Retrying in ${backoffDelay}ms...`);
      await sleep(backoffDelay);
    }
  }
}

async function runMultipleSubmissions(count = 1, delayMs = 2000) {
  console.log(`Starting ${count} submissions with ${delayMs}ms delay between each...`);
  
  for (let i = 1; i <= count; i++) {
    console.log(`\n--- Submission ${i}/${count} ---`);
    
    try {
      await submitTypingResult({ maxRetries: 3, baseDelayMs: 1500 });
    } catch (error) {
      console.error(`Submission ${i} failed:`, error.message);
    }
    
    if (i < count) {
      console.log(`Waiting ${delayMs}ms before next submission...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
  
  console.log("\n🎉 All submissions completed!");
}

runMultipleSubmissions(5).catch(console.error);
