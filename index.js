import {createHash} from "crypto";
import fetch from "node-fetch";

// Helper function for delays
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Console colors and styling
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m'
};

// Styled console functions
const log = {
  info: (msg) => console.log(`${colors.cyan}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  loading: (msg) => console.log(`${colors.blue}⏳ ${msg}${colors.reset}`),
  stats: (msg) => console.log(`${colors.magenta}📊 ${msg}${colors.reset}`),
  rocket: (msg) => console.log(`${colors.bright}${colors.green}🚀 ${msg}${colors.reset}`),
  fire: (msg) => console.log(`${colors.bright}${colors.red}🔥 ${msg}${colors.reset}`),
  header: (msg) => {
    const line = '═'.repeat(60);
    console.log(`${colors.bright}${colors.cyan}${line}${colors.reset}`);
    console.log(`${colors.bright}${colors.cyan}${msg.padStart((60 + msg.length) / 2).padEnd(60)}${colors.reset}`);
    console.log(`${colors.bright}${colors.cyan}${line}${colors.reset}`);
  },
  box: (title, content) => {
    console.log(`${colors.bright}${colors.blue}┌─ ${title} ─────────────────────────────────────────┐${colors.reset}`);
    content.forEach(line => {
      console.log(`${colors.blue}│${colors.reset} ${line.padEnd(50)} ${colors.blue}│${colors.reset}`);
    });
    console.log(`${colors.bright}${colors.blue}└──────────────────────────────────────────────────┘${colors.reset}`);
  }
};

function generateSHA256Hash(input) {
  return createHash('sha256').update(input).digest('hex');
}

async function createAntiCheatHash(walletAddress, stats) {
  const generateValidationString = (wallet, wpm, accuracy, time, correct, incorrect) => {
    const combined = correct + incorrect;
    let baseValue = 0 + 23 * wpm + 89 * accuracy + 41 * time + 67 * correct + 13 * incorrect + 97 * combined;
    
    let charSum = 0;
    for (let i = 0; i < wallet.length; i++) {
      charSum += wallet.charCodeAt(i) * (i + 1);
    }
    
    baseValue += 31 * charSum;
    const finalHash = Math.floor(0x178ba57548d * baseValue % Number.MAX_SAFE_INTEGER);
    
    return `${wallet.toLowerCase()}_${wpm}_${accuracy}_${time}_${correct}_${incorrect}_${finalHash}`;
  };

  const validationString = generateValidationString(
    walletAddress, 
    stats.wpm, 
    stats.accuracy, 
    stats.time, 
    stats.correctChars, 
    stats.incorrectChars
  );
  
  const fullHash = generateSHA256Hash(validationString);
  return fullHash.substring(0, 32);
}

function generateHumanLikeStats() {
  const timeOptions = [15, 30, 60, 120];
  const selectedTime = timeOptions[Math.floor(Math.random() * timeOptions.length)];
  
  const baseWPM = Math.floor(Math.random() * 40) + 35;
  const wpmVariation = Math.floor(Math.random() * 10) - 5;
  const finalWPM = Math.max(25, baseWPM + wpmVariation);
  
  const accuracyBase = Math.floor(Math.random() * 15) + 85;
  const accuracy = Math.min(100, accuracyBase + Math.floor(Math.random() * 5));
  
  const avgCharsPerWord = 5;
  const totalCharsTyped = Math.floor((finalWPM * avgCharsPerWord * selectedTime) / 60);
  const correctChars = Math.floor((totalCharsTyped * accuracy) / 100);
  const incorrectChars = totalCharsTyped - correctChars;
  
  return {
    wpm: finalWPM,
    accuracy: accuracy,
    time: selectedTime,
    correctChars: correctChars,
    incorrectChars: Math.max(0, incorrectChars),
    progressData: []
  };
}

async function submitTypingResult({ maxRetries = 3, baseDelayMs = 1500 } = {}) {
  const walletAddress = "******************************************"; //address mu
  const gameStats = generateHumanLikeStats();

  log.stats(`Generated stats: ${colors.bright}${gameStats.wpm} WPM${colors.reset}, ${colors.bright}${gameStats.accuracy}% accuracy${colors.reset}, ${colors.bright}${gameStats.time}s${colors.reset}`);

  const antiCheatHash = await createAntiCheatHash(walletAddress, gameStats);
  const submissionPayload = {
    walletAddress,
    gameStats,
    antiCheatHash,
    timestamp: Date.now()
  };

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      log.loading(`Attempt ${colors.bright}${attempt}${colors.reset}/${colors.bright}${maxRetries}${colors.reset} - Sending to SpriteType...`);

      const response = await fetch("https://spritetype.irys.xyz/api/submit-result", {
        method: "POST",
        headers: {
          "accept": "*/*",
          "accept-language": "id-ID,id;q=0.9,en-US;q=0.8,en;q=0.7",
          "content-type": "application/json",
          "origin": "https://spritetype.irys.xyz",
          "referer": "https://spritetype.irys.xyz/",
          "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        },
        body: JSON.stringify(submissionPayload)
      });

      // Check if response is successful
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.text();
      log.info(`Server response: ${colors.dim}${result.substring(0, 100)}...${colors.reset}`);

      if (result.includes('"success":true')) {
        log.success(`Successfully submitted to leaderboard! 🎯`);
        return result;
      } else {
        log.error("Server returned failure response");
        throw new Error("Server returned failure response");
      }

    } catch (error) {
      log.error(`Attempt ${attempt} failed: ${colors.dim}${error.message}${colors.reset}`);

      // Don't retry for certain errors (4xx except 429)
      if (error.message.includes('HTTP 4') && !error.message.includes('HTTP 429')) {
        log.error("Client error detected - not retrying");
        throw error;
      }

      // If this was the last attempt, throw the error
      if (attempt === maxRetries) {
        log.error("All retry attempts exhausted 💀");
        throw error;
      }

      // Calculate exponential backoff delay
      const backoffDelay = baseDelayMs * Math.pow(2, attempt - 1);
      log.warning(`Retrying in ${colors.bright}${(backoffDelay/1000).toFixed(1)}s${colors.reset}... 🔄`);
      await sleep(backoffDelay);
    }
  }
}

async function runMultipleSubmissions(count = 1, delayMs = 2000) {
  log.header(`🤖 SPRITETYPE AUTO SUBMITTER v2.0 🤖`);

  log.box("🎯 Mission Parameters", [
    `Total submissions: ${colors.bright}${count}${colors.reset}`,
    `Delay between runs: ${colors.bright}${(delayMs/1000).toFixed(1)}s${colors.reset}`,
    `Max retries per submission: ${colors.bright}3${colors.reset}`,
    `Target: ${colors.bright}spritetype.irys.xyz${colors.reset}`
  ]);

  let successCount = 0;
  let failCount = 0;

  for (let i = 1; i <= count; i++) {
    console.log(`\n${colors.bright}${colors.bgBlue} SUBMISSION ${i}/${count} ${colors.reset}`);
    log.rocket(`Launching submission ${i}...`);

    const startTime = Date.now();

    try {
      await submitTypingResult({ maxRetries: 3, baseDelayMs: 1500 });
      successCount++;
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      log.success(`Submission ${i} completed in ${colors.bright}${duration}s${colors.reset} ⚡`);
    } catch (error) {
      failCount++;
      const duration = ((Date.now() - startTime) / 1000).toFixed(1);
      log.error(`Submission ${i} failed after ${colors.bright}${duration}s${colors.reset}: ${colors.dim}${error.message}${colors.reset}`);
    }

    if (i < count) {
      log.loading(`Cooling down for ${colors.bright}${(delayMs/1000).toFixed(1)}s${colors.reset} before next submission... 🧊`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  // Final summary
  console.log(`\n${colors.bright}${colors.bgGreen} MISSION COMPLETE ${colors.reset}`);
  log.box("📈 Final Results", [
    `✅ Successful: ${colors.green}${successCount}${colors.reset}/${count}`,
    `❌ Failed: ${colors.red}${failCount}${colors.reset}/${count}`,
    `📊 Success Rate: ${colors.bright}${((successCount/count)*100).toFixed(1)}%${colors.reset}`,
    `⏱️  Total Runtime: ${colors.bright}${((Date.now() - Date.now()) / 1000).toFixed(1)}s${colors.reset}`
  ]);

  if (successCount === count) {
    log.fire("🔥 PERFECT SCORE! All submissions successful! 🔥");
  } else if (successCount > 0) {
    log.success("Mission partially successful! 💪");
  } else {
    log.error("Mission failed! All submissions unsuccessful 💀");
  }
}

runMultipleSubmissions(5).catch(console.error);
